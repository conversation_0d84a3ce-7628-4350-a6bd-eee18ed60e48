import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconTestingModule } from '@angular/material/icon/testing';

import { jest } from '@jest/globals';
import { MockStore, provideMockStore } from '@ngrx/store/testing';

import { TranslateTestingModule } from '@s2a-core/ng-core';
import {
  equipmentsFeature,
  instancesSelectionActions,
  InstancesState,
  LoadStatus,
  selectAllScheduleItems,
  selectEquipmentTimezone,
  selectQualifications,
  WebSharedDataAccessModule,
} from '@shift-management/web/shared/data-access/store';

import { InstancesSchedulerContainerComponent } from './instances-scheduler-container.component';

import SpiedFunction = jest.SpiedFunction;

describe('InstancesSchedulerContainerComponent', () => {
  const timezone = 'Europe/Berlin';
  let component: InstancesSchedulerContainerComponent;
  let fixture: ComponentFixture<InstancesSchedulerContainerComponent>;
  let dispatchSpy: SpiedFunction<MockStore['dispatch']>;

  const initialState: InstancesState = {
    instances: [],
    timeFrameStartTime: 0,
    timeFrameEndTime: 0,
    selectedLineId: '',
    loadStatus: LoadStatus.NotLoaded,
    loadStatusEditScheduleItem: LoadStatus.NotLoaded,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        InstancesSchedulerContainerComponent,
        TranslateTestingModule.forRoot(),
        WebSharedDataAccessModule,
        MatIconTestingModule,
        MatDialogModule,
      ],
      providers: [
        provideMockStore({
          initialState,
          selectors: [
            {
              selector: equipmentsFeature.selectEquipments,
              value: [],
            },
            { selector: selectAllScheduleItems, value: [] },
            { selector: selectEquipmentTimezone(), value: 'Pacific/Honolulu' },
            {
              selector: selectQualifications,
              value: [],
            },
          ],
        }),
      ],
    });

    fixture = TestBed.createComponent(InstancesSchedulerContainerComponent);

    component = fixture.componentInstance;
  });

  it('should create', () => {
    fixture.detectChanges();

    expect(component).toBeTruthy();
  });

  describe('loadPrevWeek', () => {
    it('should dispatch event', () => {
      fixture.detectChanges();

      component.loadPrevWeek(timezone);
      fixture.detectChanges();

      fakeAsync(() => {
        expect(dispatchSpy).toHaveBeenCalledWith(
          instancesSelectionActions.navigatedToPrevWeek({
            equipmentTimezone: timezone,
          }),
        );
      });
    });
  });

  describe('loadNextWeek', () => {
    it('should dispatch event', () => {
      fixture.detectChanges();

      component.loadNextWeek(timezone);
      fixture.detectChanges();

      fakeAsync(() => {
        expect(dispatchSpy).toHaveBeenCalledWith(
          instancesSelectionActions.navigatedToNextWeek({
            equipmentTimezone: timezone,
          }),
        );
      });
    });
  });

  describe('navigateToCurrentWeek', () => {
    it('should dispatch event', () => {
      fixture.detectChanges();

      component.navigateToCurrentWeek(timezone);
      fixture.detectChanges();

      fakeAsync(() => {
        expect(dispatchSpy).toHaveBeenCalledWith(
          instancesSelectionActions.navigatedToCurrentWeek({
            equipmentTimezone: timezone,
          }),
        );
      });
    });
  });
});
