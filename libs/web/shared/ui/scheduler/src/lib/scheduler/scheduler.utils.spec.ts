import { milliseconds } from 'date-fns';

import { ClassEnum } from '@shift-management/shared/types';
import { ScheduleItem } from '@shift-management/web/shared/data-access/templates-api';

import { SchedulerUtils } from './scheduler.utils';
import { STEP_IN_MS } from './types/constants';
import { ScheduleBlock, ScheduleTimeFrame } from './types/scheduler.model';

describe('SchedulerUtils', () => {
  describe('isTimeFrameAtStartOfShift', () => {
    it('returns true if isStart property is already set in timeFrame', () => {
      const timeFrame: ScheduleTimeFrame = { time: 1234, isStart: true };

      expect(SchedulerUtils.isTimeFrameAtStartOfShift(timeFrame)).toBe(true);
    });

    it('returns true if parent start matches timeFrame time', () => {
      const parent: Partial<ScheduleBlock> = {
        start: 1234,
      };
      const timeFrame: ScheduleTimeFrame = {
        time: 1234,
        parent: parent as ScheduleBlock,
      };

      expect(SchedulerUtils.isTimeFrameAtStartOfShift(timeFrame)).toBe(true);
    });

    it('returns false if neither isStart property is set nor parent start matches timeFrame time', () => {
      const parent: Partial<ScheduleBlock> = {
        start: 4321,
      };
      const timeFrame: ScheduleTimeFrame = {
        time: 1234,
        parent: parent as ScheduleBlock,
      };

      expect(SchedulerUtils.isTimeFrameAtStartOfShift(timeFrame)).toBe(false);
    });
  });

  describe('isTimeFrameAtEndOfShift', () => {
    it('returns true if isEnd property is already set in timeFrame', () => {
      const timeFrame: ScheduleTimeFrame = {
        time: 1234,
        isEnd: true,
      };

      expect(SchedulerUtils.isTimeFrameAtEndOfShift(timeFrame)).toBe(true);
    });

    it('returns true if parent end matches timeFrame time', () => {
      const parent: Partial<ScheduleBlock> = {
        end: 1234,
      };
      const timeFrame: ScheduleTimeFrame = {
        time: 1234,
        parent: parent as ScheduleBlock,
      };

      expect(SchedulerUtils.isTimeFrameAtEndOfShift(timeFrame)).toBe(true);
    });

    it('returns false if neither isEnd property is set nor parent end matches timeFrame time', () => {
      const parent: Partial<ScheduleBlock> = {
        end: 4321,
      };
      const timeFrame: ScheduleTimeFrame = {
        time: 1234,
        parent: parent as ScheduleBlock,
      };

      expect(SchedulerUtils.isTimeFrameAtEndOfShift(timeFrame)).toBe(false);
    });
  });

  describe('isNewItemOverlappingLeft', () => {
    const schedule: ScheduleItem[] = [
      {
        start: milliseconds({ hours: 2 }),
        end: milliseconds({ hours: 8 }),
        name: 'a',
        class: ClassEnum.Production,
      },

      {
        start: milliseconds({ hours: 16 }),
        end: milliseconds({ hours: 23 }),
        name: 'a',
        class: ClassEnum.Production,
      },
    ];
    it('returns true if new item overlaps to the left in same day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 6 }),
        end: milliseconds({ hours: 16 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item overlaps to the left in previous day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 22 }),
        end: milliseconds({ days: 1, hours: 6 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item is contained in existing item', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 3 }),
        end: milliseconds({ hours: 7 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item contains an existing item', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 1 }),
        end: milliseconds({ hours: 9 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns false if new item touches previous item in same day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 8 }),
        end: milliseconds({ hours: 10 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        false,
      );
    });

    it('returns false if new item touches previous item in previous day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 23 }),
        end: milliseconds({ days: 1, hours: 10 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        false,
      );
    });

    it('returns true if new item is contained in existing item and touches start', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 2 }),
        end: milliseconds({ hours: 7 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item is contained in existing item and touches end', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 4 }),
        end: milliseconds({ hours: 8 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns false if schedule is empty', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 11 }),
        end: milliseconds({ hours: 14 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight([], newItem)).toBe(false);
    });
  });

  describe('isNewItemOverlappingRight', () => {
    const schedule: ScheduleItem[] = [
      {
        start: milliseconds({ hours: 10 }),
        end: milliseconds({ hours: 14 }),
        name: 'a',
        class: ClassEnum.Production,
      },

      {
        start: milliseconds({ days: 1, hours: 10 }),
        end: milliseconds({ days: 1, hours: 14 }),
        name: 'a',
        class: ClassEnum.Production,
      },
    ];
    it('returns true if new item overlaps to the right in same day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 6 }),
        end: milliseconds({ hours: 12 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item overlaps to the right in next day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 22 }),
        end: milliseconds({ days: 1, hours: 12 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item is contained in existing item', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 11 }),
        end: milliseconds({ hours: 13 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item contains an existing item', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 9 }),
        end: milliseconds({ hours: 15 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns false if new item touches next item in same day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 8 }),
        end: milliseconds({ hours: 10 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        false,
      );
    });

    it('returns false if new item touches next item in next day', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 23 }),
        end: milliseconds({ days: 1, hours: 10 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        false,
      );
    });

    it('returns true if new item is contained in existing item and touches end', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 11 }),
        end: milliseconds({ hours: 14 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns true if new item is contained in existing item and touches start', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 10 }),
        end: milliseconds({ hours: 13 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingLeft(schedule, newItem)).toBe(
        true,
      );
    });

    it('returns false if schedule is empty', () => {
      const newItem: ScheduleItem = {
        start: milliseconds({ hours: 11 }),
        end: milliseconds({ hours: 14 }),
        name: 'a',
        class: ClassEnum.Production,
      };

      expect(SchedulerUtils.isNewItemOverlappingRight([], newItem)).toBe(false);
    });

    it('should return true if item contains other item for long schedule without weekOverlapHandling', () => {
      const item: ScheduleItem = {
        start: milliseconds({ hours: 10 }),
        end: milliseconds({ hours: 13 }),
        name: 'a',
        class: ClassEnum.Production,
      };
      const itemSecondWeek: ScheduleItem = {
        start: milliseconds({ weeks: 1, hours: 10 }),
        end: milliseconds({ weeks: 1, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const itemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 10 }),
        end: milliseconds({ weeks: 2, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const newItemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 8 }),
        end: milliseconds({ weeks: 2, hours: 15 }),
        name: 'b',
        class: ClassEnum.Production,
      };

      expect(
        SchedulerUtils.isNewItemOverlappingRight(
          [item, itemSecondWeek, itemThirdWeek],
          newItemThirdWeek,
          false,
        ),
      ).toBeTruthy();
    });

    it('should return true if item overlaps into other item for long schedule without weekOverlapHandling', () => {
      const item: ScheduleItem = {
        start: milliseconds({ hours: 10 }),
        end: milliseconds({ hours: 13 }),
        name: 'a',
        class: ClassEnum.Production,
      };
      const itemSecondWeek: ScheduleItem = {
        start: milliseconds({ weeks: 1, hours: 10 }),
        end: milliseconds({ weeks: 1, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const itemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 10 }),
        end: milliseconds({ weeks: 2, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const newItemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 8 }),
        end: milliseconds({ weeks: 2, hours: 11 }),
        name: 'b',
        class: ClassEnum.Production,
      };

      expect(
        SchedulerUtils.isNewItemOverlappingRight(
          [item, itemSecondWeek, itemThirdWeek],
          newItemThirdWeek,
          false,
        ),
      ).toBeTruthy();
    });

    it('should return false if item is between items in future week without weekOverlapHandling', () => {
      const item: ScheduleItem = {
        start: milliseconds({ hours: 10 }),
        end: milliseconds({ hours: 13 }),
        name: 'a',
        class: ClassEnum.Production,
      };
      const itemSecondWeek: ScheduleItem = {
        start: milliseconds({ weeks: 1, hours: 10 }),
        end: milliseconds({ weeks: 1, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const itemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 10 }),
        end: milliseconds({ weeks: 2, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const itemTwoThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, days: 1, hours: 10 }),
        end: milliseconds({ weeks: 2, days: 1, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const newItemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 15 }),
        end: milliseconds({ weeks: 2, hours: 20 }),
        name: 'b',
        class: ClassEnum.Production,
      };

      expect(
        SchedulerUtils.isNewItemOverlappingRight(
          [item, itemSecondWeek, itemThirdWeek, itemTwoThirdWeek],
          newItemThirdWeek,
          false,
        ),
      ).toBeFalsy();
    });

    it('should return false if item touches items in future week without weekOverlapHandling', () => {
      const item: ScheduleItem = {
        start: milliseconds({ hours: 10 }),
        end: milliseconds({ hours: 13 }),
        name: 'a',
        class: ClassEnum.Production,
      };
      const itemSecondWeek: ScheduleItem = {
        start: milliseconds({ weeks: 1, hours: 10 }),
        end: milliseconds({ weeks: 1, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const itemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 10 }),
        end: milliseconds({ weeks: 2, hours: 13 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const itemTwoThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 20 }),
        end: milliseconds({ weeks: 2, hours: 23 }),
        name: 'b',
        class: ClassEnum.Production,
      };
      const newItemThirdWeek: ScheduleItem = {
        start: milliseconds({ weeks: 2, hours: 15 }),
        end: milliseconds({ weeks: 2, hours: 20 }),
        name: 'b',
        class: ClassEnum.Production,
      };

      expect(
        SchedulerUtils.isNewItemOverlappingRight(
          [item, itemSecondWeek, itemThirdWeek, itemTwoThirdWeek],
          newItemThirdWeek,
          false,
        ),
      ).toBeFalsy();
    });
  });

  describe('timestampsAreMinScheduleStepSizeApart', () => {
    it('should return true if schedule has 0 size', () => {
      expect(
        SchedulerUtils.timestampsAreMinScheduleStepSizeApart(0, 0),
      ).toBeTruthy();
    });

    it('should return true if schedule smaller than step size', () => {
      expect(
        SchedulerUtils.timestampsAreMinScheduleStepSizeApart(0, STEP_IN_MS - 1),
      ).toBeTruthy();
    });

    it('should return false if schedule has exactly step size', () => {
      expect(
        SchedulerUtils.timestampsAreMinScheduleStepSizeApart(0, STEP_IN_MS),
      ).toBeFalsy();
    });

    it('should return false if schedule bigger than step size', () => {
      expect(
        SchedulerUtils.timestampsAreMinScheduleStepSizeApart(0, STEP_IN_MS * 5),
      ).toBeFalsy();
    });

    it('should return the same result when from and to parameters are exchanged', () => {
      expect(
        SchedulerUtils.timestampsAreMinScheduleStepSizeApart(STEP_IN_MS * 5, 0),
      ).toBeFalsy();
    });
  });

  describe('timestampsAreMax24HoursApart', () => {
    it('should return true if timestamps are more than 24h hours apart', () => {
      const fromTimeStamp = milliseconds({ hours: 0 });
      const toTimeStamp = milliseconds({ hours: 25 });

      expect(
        SchedulerUtils.timestampsAreMax24HoursApart(fromTimeStamp, toTimeStamp),
      ).toBeTruthy();
    });

    it('should return false if timestamps are exactly 24 hours apart', () => {
      const fromTimeStamp = milliseconds({ hours: 0 });
      const toTimeStamp = milliseconds({ hours: 24 });

      expect(
        SchedulerUtils.timestampsAreMax24HoursApart(fromTimeStamp, toTimeStamp),
      ).toBeFalsy();
    });
  });
});
