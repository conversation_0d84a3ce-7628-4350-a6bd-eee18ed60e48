import { AsyncPipe } from '@angular/common';
import { Component, ChangeDetectionStrategy } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatTableModule } from '@angular/material/table';

import { BehaviorSubject, of } from 'rxjs';

import { TableLoadingDirective } from './table-loading.directive';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [MatTableModule, TableLoadingDirective, AsyncPipe],
  standalone: true,
  template: `<table
    mat-table
    [dataSource]="(templates$ | async) ?? []"
    class="active-templates-table"
    [s2aLoadingTable]="(isLoading$ | async) ?? true"
    [s2aLoadingNumberOfRows]="numberOfLoadingRows"
  >
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>name</th>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let template; columns: displayedColumns"></tr>
  </table>`,
})
class HostComponent {
  isLoading$ = new BehaviorSubject(true);
  displayedColumns: string[] = ['name'];
  templates$ = of([]);
  numberOfLoadingRows = 3;

  finishLoading() {
    this.isLoading$.next(false);
  }
}

describe('TableLoadingDirective', () => {
  let fixture: ComponentFixture<HostComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HostComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(HostComponent);
  });

  it('should render LoadingDirective and display loadingSkeleton', () => {
    fixture.detectChanges();
    const countOfSkeletons =
      fixture.debugElement.nativeElement.querySelectorAll(
        '.mat-cell.skeleton',
      ).length;

    expect(countOfSkeletons).toBe(3);
  });

  it('should hide LoadingOverlay when loading Finished', () => {
    fixture.detectChanges();
    const countOfSkeletons =
      fixture.debugElement.nativeElement.querySelectorAll(
        '.mat-cell.skeleton',
      ).length;

    expect(countOfSkeletons).toBe(3);

    fixture.componentInstance.finishLoading();
    fixture.detectChanges();

    const countOfSkeletonsAfterLoading =
      fixture.debugElement.nativeElement.querySelectorAll(
        '.mat-cell.skeleton',
      ).length;

    expect(countOfSkeletonsAfterLoading).toBe(0);
  });

  it('should respect numberOfRows input property', () => {
    fixture.componentInstance.numberOfLoadingRows = 5;
    fixture.detectChanges();

    const countOfSkeletons =
      fixture.debugElement.nativeElement.querySelectorAll(
        '.mat-cell.skeleton',
      ).length;

    expect(countOfSkeletons).toBe(5);

    fixture.componentInstance.finishLoading();
    fixture.detectChanges();

    const countOfSkeletonsAfterLoading =
      fixture.debugElement.nativeElement.querySelectorAll(
        '.mat-cell.skeleton',
      ).length;

    expect(countOfSkeletonsAfterLoading).toBe(0);
  });
});
