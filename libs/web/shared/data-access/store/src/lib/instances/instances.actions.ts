import { createActionGroup, props, emptyProps } from '@ngrx/store';

import {
  CreateInstanceDto,
  Instance,
  UpdateInstanceDto,
} from '@shift-management/web/shared/data-access/instances-api';
import { ScheduleItem } from '@shift-management/web/shared/data-access/templates-api';

export const instancesActions = createActionGroup({
  source: 'Instances Page',
  events: {
    'Page Opened': emptyProps(),
    'Calculated Week Start And End Time': emptyProps(),
    'Set Timeframe Start And End Time': props<{
      startTime: number;
      endTime: number;
    }>(),
    'Selected Equipment By Id': props<{
      equipmentId: string;
    }>(),
  },
});

export const instancesApiActions = createActionGroup({
  source: 'Instances API',
  events: {
    'Load Requested': props<{ startTime: number; endTime: number }>(),
    'Loaded Success': props<{ instances: Instance[] }>(),
    'Loaded Failure': emptyProps(),
    'Load Skipped': emptyProps(),
  },
});

export const instancesSelectionActions = createActionGroup({
  source: 'Instances Navigation',
  events: {
    'Navigated To Prev Week': props<{ equipmentTimezone: string }>(),
    'Navigated To Next Week': props<{ equipmentTimezone: string }>(),
    'Navigated To Current Week': props<{ equipmentTimezone: string }>(),
  },
});

export const instancesEditDialogActions = createActionGroup({
  source: 'Instance Create Dialog',
  events: {
    'Instance Create Requested': props<{
      selectedLineId: string;
      createInstanceDto: CreateInstanceDto;
    }>(),
    'Instance Create Succeeded': props<{
      createdInstance: Instance;
    }>(),
    'Instance Create Failure': props<{
      message: string;
    }>(),
    'Instance Edit Requested': props<{
      instance: Instance;
      updateInstanceDto: UpdateInstanceDto;
    }>(),
    'Save Clicked': props<{
      clickedBlockScheduleItem: ScheduleItem | undefined;
      scheduleItemToSave: ScheduleItem;
    }>(),
    'Instance Edit Succeeded': props<{
      newInstance: Instance;
      originalInstance: Instance;
    }>(),
    'Instance Edit Failure': props<{
      message: string;
    }>(),
    'Instance Delete Requested': props<{
      instance: Instance;
    }>(),
    'Instance Delete Succeeded': props<{ deletedInstance: Instance }>(),
    'Instance Delete Failure': emptyProps(),
    'Instance Edit Closed': emptyProps(),
  },
});
