import { set } from 'date-fns';

import { ClassEnum } from '@shift-management/shared/types';
import { Instance } from '@shift-management/web/shared/data-access/instances-api';
import { ScheduleItem } from '@shift-management/web/shared/data-access/templates-api';

import { EquipmentsState } from '../equipment/equipments-state.model';
import { LoadStatus } from '../with-load-status.extension';

import {
  InstancesState,
  selectEquipmentTimezone,
  selectIsInstanceOrEquipmentInLoadingError,
  selectIsInstanceOrEquipmentLoading,
  selectWeekdays,
  selectWeeklySchedule,
} from './index';

describe('Instances Selectors', () => {
  describe('selectWeekdays', () => {
    it('should return weekdays for timeFrame', () => {
      const state: InstancesState = {
        instances: [],
        timeFrameStartTime: 0,
        timeFrameEndTime: 604799999,
        selectedLineId: '',
        loadStatus: LoadStatus.Loaded,
        loadStatusEditScheduleItem: LoadStatus.NotLoaded,
      };

      expect(selectWeekdays.projector(state, 'UTC')).toEqual([
        new Date('01-01-1970 00:00:00'),
        new Date('01-02-1970 00:00:00'),
        new Date('01-03-1970 00:00:00'),
        new Date('01-04-1970 00:00:00'),
        new Date('01-05-1970 00:00:00'),
        new Date('01-06-1970 00:00:00'),
        new Date('01-07-1970 00:00:00'),
      ]);
    });

    it('should return weekdays for timeFrame for -10 timezone', () => {
      const state: InstancesState = {
        instances: [],
        timeFrameStartTime: 1684749600000,
        timeFrameEndTime: 1685354399999,
        selectedLineId: '',
        loadStatus: LoadStatus.Loaded,
        loadStatusEditScheduleItem: LoadStatus.Loaded,
      };

      expect(selectWeekdays.projector(state, 'Pacific/Honolulu')).toEqual([
        new Date('22 May 2023 00:00:00'),
        new Date('23 May 2023 00:00:00'),
        new Date('24 May 2023 00:00:00'),
        new Date('25 May 2023 00:00:00'),
        new Date('26 May 2023 00:00:00'),
        new Date('27 May 2023 00:00:00'),
        new Date('28 May 2023 00:00:00'),
      ]);
    });

    it('should return weekdays for timeFrame for +5:30 timezone', () => {
      const state: InstancesState = {
        instances: [],
        timeFrameStartTime: 1684693800000,
        timeFrameEndTime: 1685298599999,
        selectedLineId: '',
        loadStatus: LoadStatus.Loaded,
        loadStatusEditScheduleItem: LoadStatus.Loaded,
      };

      expect(selectWeekdays.projector(state, 'Asia/Kolkata')).toEqual([
        new Date('22 May 2023 00:00:00'),
        new Date('23 May 2023 00:00:00'),
        new Date('24 May 2023 00:00:00'),
        new Date('25 May 2023 00:00:00'),
        new Date('26 May 2023 00:00:00'),
        new Date('27 May 2023 00:00:00'),
        new Date('28 May 2023 00:00:00'),
      ]);
    });

    it('should return empty Array for invalid timeFrame', () => {
      const state: InstancesState = {
        instances: [],
        timeFrameStartTime: 0,
        timeFrameEndTime: -10,
        selectedLineId: '',
        loadStatus: LoadStatus.Loaded,
        loadStatusEditScheduleItem: LoadStatus.NotLoaded,
      };

      expect(selectWeekdays.projector(state, 'UTC')).toEqual([]);
    });
  });

  describe('selectIsInstanceOrEquipmentInLoadingError', () => {
    it('should return true if either instance or equipment loading has error', () => {
      const mockInstanceLoadingError = true;
      const mockEquipmentLoadingError = false;

      const result = selectIsInstanceOrEquipmentInLoadingError.projector(
        mockInstanceLoadingError,
        mockEquipmentLoadingError,
      );

      expect(result).toBe(true);
    });

    it('should return false if neither instance nor equipment loading has error', () => {
      const mockInstanceLoadingError = false;
      const mockEquipmentLoadingError = false;

      const result = selectIsInstanceOrEquipmentInLoadingError.projector(
        mockInstanceLoadingError,
        mockEquipmentLoadingError,
      );

      expect(result).toBe(false);
    });
  });

  describe('selectIsInstanceOrEquipmentLoading', () => {
    it('should return true if either instance or equipment is loading and there is no error', () => {
      const mockInstanceIsLoading = true;
      const mockEquipmentIsLoading = false;
      const mockInstanceOrEquipmentIsError = false;

      const result = selectIsInstanceOrEquipmentLoading.projector(
        mockInstanceIsLoading,
        mockEquipmentIsLoading,
        mockInstanceOrEquipmentIsError,
      );

      expect(result).toBe(true);
    });

    it('should return false if either instance or equipment has error', () => {
      const mockInstanceIsLoading = false;
      const mockEquipmentIsLoading = true;
      const mockInstanceOrEquipmentIsError = true;

      const result = selectIsInstanceOrEquipmentLoading.projector(
        mockInstanceIsLoading,
        mockEquipmentIsLoading,
        mockInstanceOrEquipmentIsError,
      );

      expect(result).toBe(false);
    });

    it('should return false if neither instance nor equipment is loading', () => {
      const mockInstanceIsLoading = false;
      const mockEquipmentIsLoading = false;
      const mockInstanceOrEquipmentIsError = false;

      const result = selectIsInstanceOrEquipmentLoading.projector(
        mockInstanceIsLoading,
        mockEquipmentIsLoading,
        mockInstanceOrEquipmentIsError,
      );

      expect(result).toBe(false);
    });
  });

  describe('selectWeeklySchedule', () => {
    it('should return an empty weekly schedule when there are no instances', () => {
      const state: InstancesState = {
        instances: [],
        timeFrameStartTime: 0,
        timeFrameEndTime: 0,
        selectedLineId: '',
        loadStatus: LoadStatus.Loaded,
        loadStatusEditScheduleItem: LoadStatus.NotLoaded,
      };

      const result = selectWeeklySchedule.projector(state);

      const expected: ScheduleItem[] = [];

      expect(result).toEqual(expected);
    });

    describe('with instances available', () => {
      const unwantedItemBefore: Instance = {
        class: ClassEnum.Production,
        name: 'Shift before timeFrame',
        entityType: 'instance',
        start: set(new Date(), {
          year: 2023,
          month: 4,
          date: 1,
          hours: 14,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        end: set(new Date(), {
          year: 2023,
          month: 4,
          date: 1,
          hours: 22,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        account: 'readykit-replay',
        lineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
        instanceId: 'dfe05773-e711-4c6a-aaaa-f0368a768987',
        templateId: '4e7864a3-ed63-41b9-afc6-edc7257fa2d8',
      };

      const prevWeekItem: Instance = {
        class: ClassEnum.Production,
        name: 'Sunday Evening',
        entityType: 'instance',
        start: set(new Date(), {
          year: 2023,
          month: 4,
          date: 14,
          hours: 14,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        end: set(new Date(), {
          year: 2023,
          month: 4,
          date: 14,
          hours: 22,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        account: 'readykit-replay',
        lineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
        instanceId: 'dfe05773-e711-4c6a-aaaa-f0368a768987',
        templateId: '4e7864a3-ed63-41b9-afc6-edc7257fa2d8',
      };

      const weekOverlappingItem: Instance = {
        class: ClassEnum.Production,
        name: 'Week overlapping shift',
        entityType: 'instance',
        start: set(new Date(), {
          year: 2023,
          month: 4,
          date: 14,
          hours: 22,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        end: set(new Date(), {
          year: 2023,
          month: 4,
          date: 15,
          hours: 6,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        account: 'readykit-replay',
        lineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
        instanceId: 'dfe05773-e711-4c6a-aaaa-f0368a768987',
        templateId: '4e7864a3-ed63-41b9-afc6-edc7257fa2d8',
      };

      const queriedWeekItem: Instance = {
        class: ClassEnum.Production,
        name: 'First shift in week',
        entityType: 'instance',
        start: set(new Date(), {
          year: 2023,
          month: 4,
          date: 15,
          hours: 6,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        end: set(new Date(), {
          year: 2023,
          month: 4,
          date: 15,
          hours: 14,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        account: 'readykit-replay',
        lineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
        instanceId: 'dfe05773-e711-4c6a-aaaa-f0368a768987',
        templateId: '4e7864a3-ed63-41b9-afc6-edc7257fa2d8',
      };

      const nextWeekItem: Instance = {
        class: ClassEnum.Production,
        name: 'First shift in next week',
        entityType: 'instance',
        start: set(new Date(), {
          year: 2023,
          month: 4,
          date: 22,
          hours: 6,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        end: set(new Date(), {
          year: 2023,
          month: 4,
          date: 22,
          hours: 14,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        account: 'readykit-replay',
        lineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
        instanceId: 'dfe05773-e711-4c6a-aaaa-f0368a768987',
        templateId: '4e7864a3-ed63-41b9-afc6-edc7257fa2d8',
      };

      const unwantedItemAfter: Instance = {
        class: ClassEnum.Production,
        name: 'shift after timeframe',
        entityType: 'instance',
        start: set(new Date(), {
          year: 2023,
          month: 7,
          date: 15,
          hours: 6,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        end: set(new Date(), {
          year: 2023,
          month: 7,
          date: 15,
          hours: 14,
          minutes: 0,
          seconds: 0,
          milliseconds: 0,
        }).getTime(),
        account: 'readykit-replay',
        lineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
        instanceId: 'dfe05773-e711-4c6a-aaaa-f0368a768987',
        templateId: '4e7864a3-ed63-41b9-afc6-edc7257fa2d8',
      };

      const instances: Instance[] = [
        unwantedItemBefore,
        prevWeekItem,
        weekOverlappingItem,
        queriedWeekItem,
        nextWeekItem,
        unwantedItemAfter,
      ];

      const weekStartTime = set(new Date(), {
        year: 2023,
        month: 4,
        date: 15,
        hours: 0,
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
      }).getTime();
      const weekEndTime = set(new Date(), {
        year: 2023,
        month: 4,
        date: 22,
        hours: 0,
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
      }).getTime();

      it('should contain previous week`s data', () => {
        const result = selectWeeklySchedule.projector({
          instances,
          timeFrameStartTime: weekStartTime,
          timeFrameEndTime: weekEndTime,
          selectedLineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
          loadStatus: LoadStatus.NotLoaded,
          loadStatusEditScheduleItem: LoadStatus.NotLoaded,
        });

        expect(result).toContain(prevWeekItem);
      });

      it('should contain overlapping item', () => {
        const result = selectWeeklySchedule.projector({
          instances,
          timeFrameStartTime: weekStartTime,
          timeFrameEndTime: weekEndTime,
          selectedLineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
          loadStatus: LoadStatus.NotLoaded,
          loadStatusEditScheduleItem: LoadStatus.NotLoaded,
        });

        expect(result).toContain(weekOverlappingItem);
      });

      it('should contain queried week`s item', () => {
        const result = selectWeeklySchedule.projector({
          instances,
          timeFrameStartTime: weekStartTime,
          timeFrameEndTime: weekEndTime,
          selectedLineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
          loadStatus: LoadStatus.NotLoaded,
          loadStatusEditScheduleItem: LoadStatus.NotLoaded,
        });

        expect(result).toContain(queriedWeekItem);
      });

      it('should contain next week`s data', () => {
        const result = selectWeeklySchedule.projector({
          instances,
          timeFrameStartTime: weekStartTime,
          timeFrameEndTime: weekEndTime,
          selectedLineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
          loadStatus: LoadStatus.NotLoaded,
          loadStatusEditScheduleItem: LoadStatus.NotLoaded,
        });

        expect(result).toContain(nextWeekItem);
      });

      it('should not contain items > 1 week before or after the timeframe', () => {
        const result = selectWeeklySchedule.projector({
          instances,
          timeFrameStartTime: weekStartTime,
          timeFrameEndTime: weekEndTime,
          selectedLineId: '1d4db519-e9c3-4e83-b64d-dc52148382ed',
          loadStatus: LoadStatus.NotLoaded,
          loadStatusEditScheduleItem: LoadStatus.NotLoaded,
        });

        expect(result).not.toContain(unwantedItemBefore);
        expect(result).not.toContain(unwantedItemAfter);
      });
    });
  });

  describe('selectEquipmentTimezone', () => {
    let state: EquipmentsState;
    beforeEach(() => {
      state = {
        equipments: [
          {
            equipmentId: 'testLineId',
            timezone: 'Europe/Berlin',
            account: 'ready-kit-dummy',
            description: 'Line12',
            level: 'line',
            plantId: '153f9740-0be7-4ed2-a8a2-0cb0bb86e7d8',
            properties: [
              {
                propertyName: 'hasReadyKit',
                propertyValue: 'true',
              },
            ],
            techDesc: '123465',
            version: 1,
          },
          {
            equipmentId: 'testLineId1',
            timezone: 'Europe/Berlin',
            account: 'ready-kit-dummy',
            description: 'Line123',
            level: 'line',
            plantId: '153f9740-0be7-4ed2-a8a2-0cb0bb86e7d8',
            properties: [
              {
                propertyName: 'hasReadyKit',
                propertyValue: 'true',
              },
            ],
            techDesc: '123465',
            version: 1,
          },
        ],
        loadStatus: LoadStatus.Loaded,
      };
    });

    it('should return timezone for given lineId', () => {
      expect(
        selectEquipmentTimezone().projector(state.equipments, 'testLineId'),
      ).toBe('Europe/Berlin');
    });

    it('should return UTC for non-existing lineId', () => {
      expect(
        selectEquipmentTimezone().projector(state.equipments, 'testLineId3'),
      ).toBe('Etc/UTC');
    });
  });
});
