import { createFeature, createReducer, on } from '@ngrx/store';

import { uiActions } from './ui.actions';

export interface UiState {
  notConfigured: boolean;
}
export const initialUiState: UiState = {
  notConfigured: false,
};

export const uiReducer = createReducer<UiState>(
  initialUiState,
  on(
    uiActions.notConfigured,
    (state): UiState => ({
      ...state,
      notConfigured: true,
    }),
  ),
);

export const uiFeature = createFeature({
  name: 'ui',
  reducer: uiReducer,
});
