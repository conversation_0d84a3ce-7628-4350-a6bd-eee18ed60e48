import { Injectable } from '@angular/core';

import { ComponentStore } from '@ngrx/component-store';

import { Template } from '@shift-management/web/shared/data-access/templates-api';

export interface TemplatesPreviewState {
  isEditing: boolean;
  originalTemplate?: Template;
  modifyTemplate?: Template;
  isSaved: boolean;
  isCanceled: boolean;
}

@Injectable()
export class TemplatesPreviewComponentStore extends ComponentStore<TemplatesPreviewState> {
  // Selector
  selectIsEditingMode$ = this.select((state) => state.isEditing);
  selectModifyTemplate$ = this.select((state) => state.modifyTemplate);
  selectIsSaved$ = this.select((state) => state.isSaved);
  selectIsCanceled$ = this.select((state) => state.isCanceled);

  // Reducer
  readonly setEditing = this.updater(
    (state, isEditing: boolean): TemplatesPreviewState => ({
      ...state,
      isEditing,
      isSaved: false,
    }),
  );

  readonly save = this.updater(
    (state): TemplatesPreviewState => ({
      ...state,
      isEditing: false,
      isSaved: true,
    }),
  );

  readonly cancel = this.updater(
    (state): TemplatesPreviewState => ({
      ...state,
      isEditing: false,
      isSaved: false,
      isCanceled: true,
      modifyTemplate: state.originalTemplate,
    }),
  );

  readonly setOriginalTemplate = this.updater(
    (state, template: Template | undefined): TemplatesPreviewState => ({
      ...state,
      originalTemplate: template,
      modifyTemplate: template,
    }),
  );

  readonly updateTemplate = this.updater(
    (state, newModifyTemplate: Partial<Template>): TemplatesPreviewState => {
      if (state.modifyTemplate) {
        return {
          ...state,
          modifyTemplate: {
            ...state.modifyTemplate,
            ...newModifyTemplate,
          },
        };
      }

      return state;
    },
  );

  constructor() {
    super({
      isEditing: false,
      originalTemplate: undefined,
      modifyTemplate: undefined,
      isSaved: false,
      isCanceled: false,
    });
  }
}
