import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { Actions } from '@ngrx/effects';
import { provideMockActions } from '@ngrx/effects/testing';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { Observable } from 'rxjs';

import { TranslateTestingModule } from '@s2a-core/ng-core';
import {
  activeTemplatesLoadStatusSelectors,
  selectQualifications,
  templatesLoadStatusSelectors,
} from '@shift-management/web/shared/data-access/store';
import { TemplatesCreateDialogComponent } from '@shift-management/web/templates/templates-create-dialog';

import { TemplatesFeatureShellComponent } from './templates-feature-shell.component';

describe('ShiftFeatureTemplatesComponent', () => {
  let component: TemplatesFeatureShellComponent;
  let fixture: ComponentFixture<TemplatesFeatureShellComponent>;
  let actions$ = new Observable<unknown>();
  let store: MockStore;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TemplatesFeatureShellComponent,
        TranslateTestingModule.forRoot(),
        NoopAnimationsModule,
        MatDialogModule,
      ],
      providers: [
        provideMockActions(() => actions$),
        provideMockStore(),
        provideMockStore({
          selectors: [
            {
              selector: selectQualifications,
              value: [],
            },
          ],
        }),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    actions$ = TestBed.inject(Actions);
    store = TestBed.inject(MockStore);
    fixture = TestBed.createComponent(TemplatesFeatureShellComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have button that opens TemplatesCreateDialogComponent', () => {
    const dialog = fixture.debugElement.injector.get(MatDialog);
    const spy = jest.spyOn(dialog, 'open');
    const button = fixture.debugElement.nativeElement.querySelector('button');
    button.click();

    expect(spy).toHaveBeenCalledWith(
      TemplatesCreateDialogComponent,
      expect.anything(),
    );
  });

  describe('while active templates are not loaded or in error state', () => {
    it('should disable the add button', () => {
      activeTemplatesLoadStatusSelectors.selectIsLoaded.setResult(false);
      store.refreshState();
      const button = fixture.debugElement.nativeElement.querySelector(
        '.button-open-create-dialog',
      );
      fixture.detectChanges();

      expect(button.disabled).toBeTruthy();
    });
  });

  describe('when active templates are successfully loaded', () => {
    it('should enable the add button', () => {
      templatesLoadStatusSelectors.selectIsLoaded.setResult(true);
      store.refreshState();
      const button = fixture.debugElement.nativeElement.querySelector(
        '.button-open-create-dialog',
      );
      fixture.detectChanges();

      expect(button.disabled).toBeFalsy();
    });
  });
});
