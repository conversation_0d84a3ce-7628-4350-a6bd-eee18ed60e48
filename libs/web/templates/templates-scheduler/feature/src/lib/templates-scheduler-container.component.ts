import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  ChangeDetectionStrategy,
  inject,
  EventEmitter,
  Output,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';

import { Store } from '@ngrx/store';

import { TranslationModule } from '@s2a-core/ng-core';
import {
  selectQualifications,
  templatesLoadStatusSelectors,
} from '@shift-management/web/shared/data-access/store';
import { ScheduleItem } from '@shift-management/web/shared/data-access/templates-api';
import {
  SchedulerComponent,
  ScheduleItemEditDialogComponent,
  ScheduleItemEditDialogData,
  ScheduleBlock,
} from '@shift-management/web/shared/ui/scheduler';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 's2a-templates-scheduler-container',
  imports: [CommonModule, TranslationModule, SchedulerComponent],
  template: `
    <s2a-scheduler
      [isEditable]="isEditable"
      [isLoading]="(isLoading$ | async) ?? true"
      [isError]="(isLoadingError$ | async) ?? false"
      [errorMessage]="
        'components.shift_management.shared.api.errors.templates.failed_load_long'
          | translate
      "
      [scheduleItems]="scheduleItems"
      (blockClicked)="openDialog($event)"
      [qualifications]="qualifications() ?? []"
    ></s2a-scheduler>
  `,
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class TemplatesSchedulerContainerComponent {
  @Input() isEditable = false;
  @Input() scheduleItems: ScheduleItem[] = [];
  @Output() readonly scheduleChanged = new EventEmitter<ScheduleItem[]>();

  private dialog = inject(MatDialog);
  private store = inject(Store);

  isLoading$ = this.store.select(templatesLoadStatusSelectors.selectIsLoading);
  isLoadingError$ = this.store.select(
    templatesLoadStatusSelectors.selectIsError,
  );
  qualifications = toSignal(this.store.select(selectQualifications));

  openDialog({
    block,
    scheduleItems,
  }: {
    block: ScheduleBlock;
    scheduleItems: ScheduleItem[];
  }) {
    if (!this.isEditable) return;
    const qualifications = this.qualifications();
    const data: ScheduleItemEditDialogData = {
      schedule: scheduleItems,
      item: block,
      qualifications: qualifications ?? [],
    };

    const dialogRef = this.dialog.open(ScheduleItemEditDialogComponent, {
      disableClose: true,
      autoFocus: true,
      data,
    });

    dialogRef.afterClosed().subscribe((dialogResult) => {
      if (dialogResult) {
        this.scheduleChanged.emit(dialogResult);
      }
    });
  }
}
