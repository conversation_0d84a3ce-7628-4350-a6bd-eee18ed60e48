import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl } from '@angular/forms';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { BrowserDynamicTestingModule } from '@angular/platform-browser-dynamic/testing';

import { jest } from '@jest/globals';
import { MockStore, provideMockStore } from '@ngrx/store/testing';

import { TranslateTestingModule } from '@s2a-core/ng-core';
import {
  templateCreationStatusSelectors,
  templatesCreateDialogActions,
} from '@shift-management/web/shared/data-access/store';

import { TemplatesCreateDialogComponent } from './templates-create-dialog.component';

describe('TemplatesCreateDialogComponent', () => {
  let component: TemplatesCreateDialogComponent;
  let fixture: ComponentFixture<TemplatesCreateDialogComponent>;
  let store: MockStore;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TemplatesCreateDialogComponent,
        MatDialogModule,
        BrowserDynamicTestingModule,
        NoopAnimationsModule,
        TranslateTestingModule.forRoot(),
      ],
      providers: [
        { provide: MatDialogRef, useValue: {} },
        provideMockStore({
          selectors: [
            {
              selector: templateCreationStatusSelectors.selectIsLoading,
              value: false,
            },
            {
              selector: templateCreationStatusSelectors.selectIsError,
              value: false,
            },
            {
              selector: templateCreationStatusSelectors.selectIsLoaded,
              value: false,
            },
          ],
        }),
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(TemplatesCreateDialogComponent);
    component = fixture.componentInstance;
    store = TestBed.inject(MockStore);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return null if the control value is not in templateNames', () => {
    component.templateNames = ['templateName1', 'templateName2'];
    const control = new FormControl('templateName3');
    const result = component.templateNameExistsValidator()(control);

    expect(result).toBeNull();
  });

  it('should return an object with "templateNameExists" property if the control value is in templateNames', () => {
    component.templateNames = ['templateName1', 'templateName2'];
    const control = new FormControl('templateName2');
    const result = component.templateNameExistsValidator()(control);

    expect(result).toEqual({ templateNameExists: true });
  });

  it('should close Dialog when isLoaded', () => {
    const spy = jest.spyOn(component, 'closeDialog');
    templateCreationStatusSelectors.selectIsLoaded.setResult(true);
    store.refreshState();
    fixture.detectChanges();

    expect(spy).toHaveBeenCalled();
  });

  it('should dispatch templateCreationRequested actions', () => {
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    component.name.patchValue('name');
    component.shiftsPerDay.patchValue(3);
    component.shiftStartTime.patchValue('00:00');
    fixture.detectChanges();
    const submitButton = fixture?.nativeElement?.querySelector(
      'button[id="submitButton"]',
    );
    submitButton.click();

    expect(dispatchSpy).toHaveBeenCalledWith(
      templatesCreateDialogActions.templateCreationRequested({
        template: {
          name: 'name',
          schedule: expect.anything(),
          shiftsPerDay: 3,
          shiftStartTime: '00:00',
        },
      }),
    );
  });

  it('should disable button when form is invalid', () => {
    component.name.patchValue('name');
    component.shiftsPerDay.patchValue(3);
    component.shiftStartTime.patchValue('00:13');
    fixture.detectChanges();
    const submitButton = fixture?.nativeElement?.querySelector(
      'button[id="submitButton"]',
    );

    expect(submitButton.disabled).toBe(true);
  });
});
