import { CommonModule } from '@angular/common';
import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  OnDestroy,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
  ValidatorFn,
  ValidationErrors,
  AbstractControl,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressBarModule } from '@angular/material/progress-bar';

import { Store } from '@ngrx/store';
import { map, tap, Subscription, Observable, filter } from 'rxjs';

import { TranslationModule } from '@s2a-core/ng-core';
import { calculateSchedule } from '@shift-management/shared/templates';
import {
  templateCreationStatusSelectors,
  templatesCreateDialogActions,
  templatesFeature,
} from '@shift-management/web/shared/data-access/store';
import { CreateTemplate } from '@shift-management/web/shared/data-access/templates-api';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 's2a-templates-create-dialog',
  imports: [
    CommonModule,
    MatDialogModule,
    FormsModule,
    MatInputModule,
    MatButtonModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatListModule,
    MatIconModule,
    MatProgressBarModule,
    TranslationModule,
  ],
  templateUrl: './templates-create-dialog.component.html',
  styleUrls: ['./templates-create-dialog.component.scss'],
})
export class TemplatesCreateDialogComponent implements OnInit, OnDestroy {
  private templateNames$: Observable<string[]> | undefined;
  private subscription = new Subscription();
  templateNames: string[] = [];
  store = inject(Store);
  createTemplateForm = new FormGroup({
    name: new FormControl('', {
      nonNullable: true,
      validators: [
        (control: AbstractControl) => Validators.required(control),
        this.templateNameExistsValidator(),
      ],
    }),
    shiftsPerDay: new FormControl(3, {
      nonNullable: true,
    }),
    shiftStartTime: new FormControl('00:00', {
      nonNullable: true,
      validators: [
        (control: AbstractControl) => Validators.required(control),
        Validators.pattern('^[0-2][0-9]:(00|15|30|45)$'),
      ],
    }),
  });

  isLoading$ = this.store.select(
    templateCreationStatusSelectors.selectIsLoading,
  );
  isLoadingError$ = this.store.select(
    templateCreationStatusSelectors.selectIsError,
  );
  isLoaded$ = this.store.select(templateCreationStatusSelectors.selectIsLoaded);

  constructor(public dialogRef: MatDialogRef<TemplatesCreateDialogComponent>) {}

  ngOnInit() {
    this.templateNames$ = this.store
      .select(templatesFeature.selectTemplatesState)
      .pipe(
        map((templatesState) => {
          const templateNames = templatesState.templates.map(
            (template) => template.name,
          );
          return templateNames;
        }),
        tap((templateNames) => {
          this.templateNames = templateNames;
        }),
      );
    this.subscription.add(this.templateNames$.subscribe());
    this.subscription.add(
      this.isLoaded$
        .pipe(filter((isLoaded) => isLoaded))
        .subscribe((_) => this.closeDialog()),
    );
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  closeDialog() {
    this.dialogRef.close();
  }

  get name() {
    return this.createTemplateForm.controls.name;
  }
  get shiftStartTime() {
    return this.createTemplateForm.controls.shiftStartTime;
  }
  get shiftsPerDay() {
    return this.createTemplateForm.controls.shiftsPerDay;
  }

  submit() {
    if (this.createTemplateForm.valid) {
      const formData = this.createTemplateForm.value;
      const template: CreateTemplate = {
        shiftsPerDay: formData.shiftsPerDay!,
        shiftStartTime: formData.shiftStartTime!,
        name: formData.name!,
        schedule: calculateSchedule(
          formData.shiftStartTime!,
          formData.shiftsPerDay!,
          24 / formData.shiftsPerDay!,
        ),
      };
      this.store.dispatch(
        templatesCreateDialogActions.templateCreationRequested({ template }),
      );
    }
  }

  templateNameExistsValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const templateNameExists = this.templateNames.includes(control.value);
      return templateNameExists ? { templateNameExists: true } : null;
    };
  }
}
