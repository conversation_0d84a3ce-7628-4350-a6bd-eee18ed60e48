import { ChangeDetectionStrategy, Component } from '@angular/core';

import { Store } from '@ngrx/store';

import { activeTemplatesApiAction } from '@shift-management/web/shared/data-access/store';
import { TemplatesAssignmentTableComponent } from '@shift-management/web/template-assignment/table/feature/web-template-assignment-table';

@Component({
  selector: 's2a-template-assignment',
  imports: [TemplatesAssignmentTableComponent],
  templateUrl: './template-assignment-feature-shell.component.html',
  styleUrls: ['./template-assignment-feature-shell.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TemplateAssignmentFeatureShellComponent {
  constructor(store: Store) {
    store.dispatch(activeTemplatesApiAction.loadRequested());
  }
}
