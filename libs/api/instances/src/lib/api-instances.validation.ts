import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { milliseconds, minutesToSeconds } from 'date-fns';

export const IsMultipleOfQuarterHour = (
  validationOptions?: ValidationOptions,
) =>
  function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isMultipleOfQuarterHour',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: {
        validate: (value: unknown) =>
          typeof value === 'number' && value % minutesToSeconds(15) === 0,
        defaultMessage: (): string =>
          `$property must be a number and a multiple of 15 minutes`,
      },
    });
  };
export const IsValidEnd = (
  startKeyName: string,
  validationOptions?: ValidationOptions,
) =>
  function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidEnd',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [startKeyName],
      options: validationOptions,
      validator: {
        validate: (end: unknown, args: ValidationArguments) => {
          const obj = args.object;
          return (
            typeof end === 'number' &&
            startKeyName in obj &&
            typeof (obj as never)[startKeyName] === 'number' &&
            (obj as never)[startKeyName] < end &&
            end - (obj as never)[startKeyName] <= milliseconds({ days: 1 })
          );
        },
        defaultMessage: (): string =>
          `$property must be a number, greater than ${startKeyName} (which has to exist) and the shift duration may not be more than 24h.`,
      },
    });
  };
