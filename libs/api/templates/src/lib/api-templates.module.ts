import { Module } from '@nestjs/common';

import { ApiEquipmentService } from '@shift-management/api/equipment';
import { DynamoDbClientModule } from '@shift-management/api/util/dynamodb-client';

import { ActiveTemplatesController } from './active-templates.controller';
import { ActiveTemplatesService } from './active-templates.service';
import { ApiTemplatesController } from './api-templates.controller';
import { ApiTemplatesService } from './api-templates.service';

@Module({
  imports: [DynamoDbClientModule],
  controllers: [ActiveTemplatesController, ApiTemplatesController],
  providers: [ApiEquipmentService, ActiveTemplatesService, ApiTemplatesService],
  exports: [ApiTemplatesService],
})
export class ApiTemplatesModule {}
