{"name": "shared-equipments", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/equipments/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/equipments/jest.config.ts"}}}, "tags": ["type:util", "scope:shared"]}