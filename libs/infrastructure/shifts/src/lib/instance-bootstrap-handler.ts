import { resolve } from 'path';

import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { EventBus, EventPattern, Rule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction as LambdaTarget } from 'aws-cdk-lib/aws-events-targets';
import { IQueue } from 'aws-cdk-lib/aws-sqs';
import { Construct } from 'constructs';

import { LambdaConstruct } from '@shift-management/infrastructure/constructs';

export const instanceBootstrapHandler = (
  stack: Construct,
  equipmentTable: ITable,
  accountLineSQS: IQueue,
  stage: string,
) => {
  const instanceBootstrapHandlerDistPath = resolve(
    './dist/apps/backend/instance-bootstrap-handler',
  );

  const name = 'shifts-instance-bootstrap-handler';
  const instanceBootstrapHandlerLambda = new LambdaConstruct(
    stack,
    name,
    'main.handler',
    instanceBootstrapHandlerDistPath,
    {
      LOG_LEVEL: 'WARN',
      ACCOUNT_LINE_SQS_URL: accountLineSQS.queueUrl,
    },
    instanceBootstrapHandlerDistPath,
    stage,
    name,
    2048,
    30,
    true,
  );
  equipmentTable.grantReadData(instanceBootstrapHandlerLambda.lambdaFunction);
  accountLineSQS.grantSendMessages(
    instanceBootstrapHandlerLambda.lambdaFunction,
  );
  const target = new LambdaTarget(
    instanceBootstrapHandlerLambda.lambdaFunction,
  );

  const eventPattern: EventPattern = {
    detail: { service: ['shift'], action: ['instances:bootstrap-request'] },
  };

  const s2aEventBusLocal = EventBus.fromEventBusName(
    stack,
    's2a-bus-local',
    's2a-isc-eventbridge-eventbus-local',
  );

  const description =
    'This rules will trigger instance bootstrap lambda, \
    which in turn will fetch all the instances of line since inception, for the downstream service';

  const ruleName = 'instance-bootstrap-request-downstream';
  new Rule(stack, `rule-local`, {
    eventBus: s2aEventBusLocal,
    targets: [target],
    eventPattern: eventPattern,
    ruleName: ruleName,
    description: description,
  });
};
