/* eslint-disable jest/expect-expect */

import * as cdk from 'aws-cdk-lib';
import { Stack, StackProps } from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { Construct } from 'constructs';

import { ShiftDynamodbConstruct } from './shift-dynamodb.construct';

class ShiftDynamoDbTestStack extends Stack {
  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, props);
    new ShiftDynamodbConstruct(this, 'ShiftDataTable');
  }
}

describe('ShiftsApiStack', () => {
  const app = new cdk.App({ context: { stage: 'dev' } });

  const shiftsApiStack = new ShiftDynamoDbTestStack(app, 'ShiftsApiStack', {
    env: { account: 'test', region: 'test' },
  });

  // Prepare the stack for assertions.
  const template = Template.fromStack(shiftsApiStack);

  test('dynamodb LocalSecondaryIndexes test', () => {
    template.hasResourceProperties('AWS::DynamoDB::Table', {
      TableName: 'ShiftsApiStack-shift-data',
      LocalSecondaryIndexes: [
        {
          IndexName: 'Instances',
          KeySchema: [
            {
              AttributeName: 'PK',
              KeyType: 'HASH',
            },
            {
              AttributeName: 'LSI1_SK',
              KeyType: 'RANGE',
            },
          ],
        },
      ],
    });
  });

  test('dynamodb AttributeDefinitions test', () => {
    template.hasResourceProperties('AWS::DynamoDB::Table', {
      TableName: 'ShiftsApiStack-shift-data',
      AttributeDefinitions: [
        { AttributeName: 'PK', AttributeType: 'S' },
        { AttributeName: 'SK', AttributeType: 'S' },
        { AttributeName: 'LSI1_SK', AttributeType: 'N' },
      ],
    });
  });
});
