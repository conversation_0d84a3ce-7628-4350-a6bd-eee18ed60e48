/* eslint-disable jest/expect-expect */
import { Duration, RemovalPolicy, Stack, StackProps } from 'aws-cdk-lib';
import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { LifecycleRule } from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';

import { S3Construct } from './s3.construct';

class S3TestStack extends Stack {
  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, props);
    const lifeCycleRule: LifecycleRule = {
      id: 'id',
      enabled: true,
      expiration: Duration.days(5),
    };
    const removalPolicy: RemovalPolicy = RemovalPolicy.RETAIN;

    new S3Construct(this, 'mybucket', 'stage', [lifeCycleRule], removalPolicy);
  }
}

describe('S3TestStack', () => {
  const app = new cdk.App({ context: { stage: 'dev' } });

  const s3TestStack = new S3TestStack(app, 'S3TestStack', {
    env: { account: 'test', region: 'test' },
  });

  // Prepare the stack for assertions.
  const template = Template.fromStack(s3TestStack);

  test('template should have a bucket', () => {
    template.hasResource('AWS::S3::Bucket', {});
  });

  test('bucket should have correct name', () => {
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketName: 'mybucket-stage',
    });
  });

  test('bucket should be encrypted', () => {
    template.hasResourceProperties('AWS::S3::Bucket', {
      BucketEncryption: {
        ServerSideEncryptionConfiguration: [
          { ServerSideEncryptionByDefault: { SSEAlgorithm: 'AES256' } },
        ],
      },
    });
  });

  test('bucket should be retained when stack is updated', () => {
    template.hasResource('AWS::S3::Bucket', {
      UpdateReplacePolicy: 'Retain',
    });
  });

  test('bucket should have lifecycle rule that objects expire in 5 days', () => {
    template.hasResourceProperties('AWS::S3::Bucket', {
      LifecycleConfiguration: {
        Rules: [
          {
            ExpirationInDays: 5,
            Id: 'id',
            Status: 'Enabled',
          },
        ],
      },
    });
  });
});
