import { Duration } from 'aws-cdk-lib';
import { IEventBus, Rule } from 'aws-cdk-lib/aws-events';
import { SqsQueue as QueueTarget } from 'aws-cdk-lib/aws-events-targets';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { Construct } from 'constructs';

import { LambdaConstruct } from './lambda.construct';
import { SqsConstruct } from './sqs.construct';

export interface LambdaProps {
  lambdaHandler: string;
  assetPath: string;
  env: Record<string, string>;
  layerAssetPath: string;
  stage: string;
  functionName?: string;
  memorySize?: number;
  timeoutInSeconds?: number;
}

export interface RuleProps {
  eventBus: IEventBus;
  detail:
    | ISCEquipmentDetail
    | ShiftServiceNewImageDetail
    | ShiftServiceOldImageDetail
    | ShiftEquipmentPipeDetail
    | IAMAccountDetail
    | AccountTreeDetail;
  ruleName: string;
  source?: string[];
  description?: string;
  enabled?: boolean;
}

export interface QueueProps {
  maxConcurrency: number;
  batchSize: number;
  maxReceiveCount: number;
  reportBatchItemFailures?: boolean;
  visibilityTimeout?: Duration;
}

export interface ISCEquipmentDetail {
  action: string[];
  data: {
    level: string[];
  };
  requester?: string[];
}

export interface AccountTreeDetail {
  action: string[];
  requester?: string[];
}

export interface IAMAccountDetail {
  action: string[];
  data: {
    id: [{ exists: boolean }];
    state: string[];
  };
}

interface PipeDetail {
  eventSource: string[];
  eventName: string[];
}

export interface ShiftEquipmentPipeDetail extends PipeDetail {
  dynamodb: {
    NewImage?: {
      level: string[];
    };

    OldImage?: {
      level: string[];
    };
  };
}

export interface ShiftEntityFilterType {
  entityType: string[];
  [x: string]: string[];
}

export interface ShiftServiceNewImageDetail extends PipeDetail {
  dynamodb: {
    NewImage: ShiftEntityFilterType;
  };
}

export interface ShiftServiceOldImageDetail extends PipeDetail {
  dynamodb: {
    OldImage: ShiftEntityFilterType;
  };
}

export class EventComputationConstruct extends Construct {
  lambdaConstruct: LambdaConstruct;
  sqsConstruct: SqsConstruct;
  eventRule: Rule;

  constructor(
    scope: Construct,
    name: string,
    ruleProps: RuleProps,
    lambdaProps: LambdaProps,
    queueProps: QueueProps,
  ) {
    super(scope, name);

    this.lambdaConstruct = new LambdaConstruct(
      this,
      `${name}-function`,
      lambdaProps.lambdaHandler,
      lambdaProps.assetPath,
      lambdaProps.env,
      lambdaProps.layerAssetPath,
      lambdaProps.stage,
      lambdaProps.functionName,
    );

    this.sqsConstruct = new SqsConstruct(
      this,
      `${name}-sqs`,
      queueProps.maxReceiveCount,
    );

    const eventSource = new SqsEventSource(this.sqsConstruct.queue, {
      maxConcurrency: queueProps.maxConcurrency,
      batchSize: queueProps.batchSize,
      reportBatchItemFailures: queueProps.reportBatchItemFailures ?? false,
    });

    this.lambdaConstruct.lambdaFunction.addEventSource(eventSource);

    const target = new QueueTarget(this.sqsConstruct.queue);

    const {
      eventBus,
      ruleName,
      description,
      enabled = true,
      ...eventPattern
    } = ruleProps;
    this.eventRule = new Rule(this, `rule`, {
      enabled: enabled,
      eventBus: eventBus,
      targets: [target],
      eventPattern: {
        ...eventPattern,
      },
      ruleName: ruleName,
      description: description,
    });
  }
}
