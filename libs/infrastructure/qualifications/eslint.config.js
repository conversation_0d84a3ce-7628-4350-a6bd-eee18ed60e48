const baseConfig = require('#eslint-base-config');

module.exports = [
  ...baseConfig,
  {
    files: ['**/*.ts'],
    // Override or add rules here
    rules: {
      '@typescript-eslint/naming-convention': [
        'error',
        {
          filter: {
            match: true,
            regex: '/^_$/',
          },
          format: ['camelCase', 'snake_case'],
          selector: 'variableLike',
        },
        {
          format: ['UPPER_CASE'],
          modifiers: ['const', 'global'],
          selector: 'variable',
          types: ['boolean', 'string', 'number', 'array'],
        },
        {
          format: ['camelCase', 'PascalCase'],
          modifiers: ['const', 'global'],
          selector: 'variable',
          types: ['function'],
        },
        {
          format: ['PascalCase'],
          selector: 'enum',
        },
        {
          format: ['PascalCase', 'UPPER_CASE'],
          selector: 'enumMember',
        },
        {
          selector: 'property',
          format: null,
          custom: {
            regex: '^(?:[A-Z][^\\s]*\\s?)+$',
            match: true,
          },
          modifiers: ['requiresQuotes'],
        },
        {
          selector: 'property',
          format: ['PascalCase', 'camelCase', 'snake_case', 'UPPER_CASE'],
        },
        {
          format: ['UPPER_CASE', 'camelCase', 'snake_case'],
          modifiers: ['readonly'],
          selector: 'property',
        },
      ],
    },
  },
];
