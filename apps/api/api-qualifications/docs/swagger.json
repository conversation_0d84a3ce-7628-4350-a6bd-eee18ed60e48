{"openapi": "3.0.0", "paths": {"/qualifications": {"get": {"operationId": "getQualificationsForAccount", "summary": "Get qualifications for the current account", "description": "Retrieves all qualifications associated with the current account from the authorizer context", "parameters": [], "responses": {"200": {"description": "Successfully retrieved qualifications", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QualificationResponse"}}}}, "400": {"description": "Bad request"}, "500": {"description": "Internal server error"}}, "tags": ["qualifications"]}}}, "info": {"title": "shift-management-api-qualifications", "description": "Shift API Qualifications", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"QualificationText": {"type": "object", "properties": {"locale": {"type": "string"}, "description": {"type": "string"}}, "required": ["locale", "description"]}, "Qualification": {"type": "object", "properties": {"qualificationId": {"type": "string"}, "description": {"type": "string"}, "account": {"type": "string"}, "entityType": {"type": "string"}, "texts": {"type": "array", "items": {"$ref": "#/components/schemas/QualificationText"}}}, "required": ["qualificationId", "description", "account", "entityType"]}, "QualificationResponse": {"type": "object", "properties": {"qualifications": {"type": "array", "items": {"$ref": "#/components/schemas/Qualification"}}}, "required": ["qualifications"]}}}}