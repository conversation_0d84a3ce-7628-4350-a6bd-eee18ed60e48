import { Logger } from '@aws-lambda-powertools/logger';
import { SchedulerClient } from '@aws-sdk/client-scheduler';
import { SQSBatchItemFailure, SQSEvent } from 'aws-lambda';

import {
  initSentryLambda,
  setSentryTags,
  wrapSentryHandler,
} from '@s2a/sentry-helper';
import { InstanceDB } from '@shift-management/api/instances';
import { SchedulerService } from '@shift-management/api/util/scheduler-service';

const logger = new Logger({
  serviceName: 'shift-instance-delete-scheduler',
});

initSentryLambda();

export const handler = wrapSentryHandler(
  async (
    event: SQSEvent,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    context: unknown,
  ): Promise<{ batchItemFailures: SQSBatchItemFailure[] }> => {
    logger.info('Received event:', JSON.stringify(event, null, 2));
    const messagesToReprocess: SQSBatchItemFailure[] = [];
    for (const record of event.Records) {
      try {
        const deletedInstance = JSON.parse(record.body).detail.dynamodb
          .OldImage as InstanceDB;
        setSentryTags({
          account: deletedInstance.account,
          lineId: deletedInstance.lineId,
          instanceId: deletedInstance.instanceId,
        });
        const sk = deletedInstance.SK;
        const scheduleId = sk.replace('I#', '');
        if (scheduleId) {
          const schedulerService = new SchedulerService(new SchedulerClient());
          await schedulerService.deleteSchedule(scheduleId);
        }
      } catch (error) {
        logger.error('error in deleting schedule, Retrying...', {
          error,
          record,
        });
        messagesToReprocess.push({ itemIdentifier: record.messageId });
      }
    }
    return { batchItemFailures: messagesToReprocess };
  },
);
