import { SQSEvent } from 'aws-lambda';

import { handler } from './main';
const mockDelete = jest.fn();
jest.mock('@shift-management/api/equipment', () => {
  return {
    ApiEquipmentService: jest.fn().mockImplementation(() => {
      return {
        delete: mockDelete,
      };
    }),
  };
});

describe('line-delete', () => {
  beforeEach(() => {
    mockDelete.mockReset();
  });

  it('should delete equipment', async () => {
    const sqsEvent = {
      Records: [
        {
          messageId: 'string',
          body: JSON.stringify({
            detail: {
              data: {
                equipmentId: 'test-line-id',
                account: 'test-account',
                level: 'line',
              },
            },
          }),
        },
      ],
    };
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await handler(sqsEvent as SQSEvent, {});

    expect(mockDelete).toHaveBeenCalled();
    expect(mockDelete).toHaveBeenCalledWith('test-account', 'test-line-id');
  });
});
