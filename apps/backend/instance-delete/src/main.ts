import { Logger } from '@aws-lambda-powertools/logger';
import {
  DynamoDBRecord,
  EventBridgeEvent,
  SQSBatchItemFailure,
  SQSBatchResponse,
  SQSEvent,
} from 'aws-lambda';

import {
  ShiftInstanceDeletedBroadcastV4Event,
  ShiftInstanceDeletedClassV4,
} from '@s2a/isc-events-v3';
import {
  initSentryLambda,
  setSentryTags,
  wrapSentryHandler,
} from '@s2a/sentry-helper';
import { InstanceDB } from '@shift-management/api/instances';

const logger = new Logger({
  serviceName: 'shifts-instance-delete',
});

initSentryLambda();

export const handler = wrapSentryHandler(
  async (event: SQSEvent, _context: unknown): Promise<SQSBatchResponse> => {
    logger.info(`Fetching instances handler ${JSON.stringify(event)}`);
    const messagesToReprocess: SQSBatchItemFailure[] = [];
    await Promise.all(
      event.Records.map(async (record) => {
        try {
          const recordData: EventBridgeEvent<
            'Event from aws:dynamodb',
            DynamoDBRecord
          > = JSON.parse(record.body);
          if (!recordData.detail.dynamodb?.OldImage) {
            messagesToReprocess.push({
              itemIdentifier: record.messageId,
            });
            logger.error(
              `DynamodbStreamRecord is malformed. Aborting. ${JSON.stringify(record)}`,
            );
            return;
          }
          const deletedInstanceEvent =
            new ShiftInstanceDeletedBroadcastV4Event();
          const deletedInstance = JSON.parse(record.body).detail.dynamodb
            .OldImage as InstanceDB;
          setSentryTags({
            account: deletedInstance.account,
            instanceId: deletedInstance.instanceId,
            lineId: deletedInstance.lineId,
          });
          deletedInstanceEvent.setMessage({
            instanceId: deletedInstance.instanceId,
            account: deletedInstance.account,
            lineId: deletedInstance.lineId,
            start: deletedInstance.start,
            end: deletedInstance.end,
            instanceClass:
              deletedInstance.class as unknown as ShiftInstanceDeletedClassV4,
            name: deletedInstance.name,
            entityType: deletedInstance.entityType,
            templateId: deletedInstance.templateId,
          });
          deletedInstanceEvent.setSource('shift');
          await deletedInstanceEvent.publish();
        } catch (e) {
          messagesToReprocess.push({
            itemIdentifier: record.messageId,
          });
          logger.error(
            `Record: ${JSON.stringify(record)} failed with error`,
            e as Error,
          );
        }
      }),
    );
    return { batchItemFailures: messagesToReprocess };
  },
);
