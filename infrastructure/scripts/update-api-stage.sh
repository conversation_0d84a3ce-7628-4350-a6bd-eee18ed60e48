#!/bin/bash

restApiId=$(aws cloudformation list-exports --query "Exports[?Name==\`s2a-shift-service-restApiId\`].Value" --output text)
deploymentId=$(aws cloudformation list-exports --query "Exports[?Name==\`s2a-shift-service-equipment-deployment-id\`].Value" --output text)

if [ -z "$deploymentId" ]
then
    echo "deploymentId is missing"
else
    echo ">>> Update Stage"
    aws apigateway update-stage \
        --rest-api-id ${restApiId} \
        --stage-name v1 \
        --patch-operations op='replace',path='/deploymentId',value="${deploymentId}"
fi



